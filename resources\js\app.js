import './bootstrap';
import Alpine from 'alpinejs';

// Initialize Alpine.js
window.Alpine = Alpine;
Alpine.start();

// QR Code scanner functionality (for future implementation)
window.QRScanner = {
    init() {
        console.log('QR Scanner initialized');
    },
    
    scan() {
        // QR code scanning logic will be implemented here
        console.log('Scanning QR code...');
    }
};

// Plant gallery functionality
window.PlantGallery = {
    currentImage: 0,
    images: [],
    
    init(images) {
        this.images = images;
        this.currentImage = 0;
    },
    
    nextImage() {
        this.currentImage = (this.currentImage + 1) % this.images.length;
        this.updateImage();
    },
    
    prevImage() {
        this.currentImage = this.currentImage === 0 ? this.images.length - 1 : this.currentImage - 1;
        this.updateImage();
    },
    
    updateImage() {
        const img = document.querySelector('.gallery-main-image');
        if (img && this.images[this.currentImage]) {
            img.src = this.images[this.currentImage];
        }
    }
};

// Geolocation functionality for garden mapping
window.GardenMap = {
    map: null,
    userMarker: null,
    
    init(containerId, centerLat = 0, centerLng = 0) {
        if (typeof L !== 'undefined') {
            this.map = L.map(containerId).setView([centerLat, centerLng], 13);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(this.map);
            
            this.getUserLocation();
        }
    },
    
    getUserLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    
                    if (this.userMarker) {
                        this.map.removeLayer(this.userMarker);
                    }
                    
                    this.userMarker = L.marker([lat, lng])
                        .addTo(this.map)
                        .bindPopup('Your Location')
                        .openPopup();
                    
                    this.map.setView([lat, lng], 15);
                },
                (error) => {
                    console.log('Geolocation error:', error);
                }
            );
        }
    },
    
    addPlantMarker(lat, lng, plantName, plantUrl) {
        if (this.map) {
            L.marker([lat, lng])
                .addTo(this.map)
                .bindPopup(`<a href="${plantUrl}" class="text-verdant-600 hover:text-verdant-800">${plantName}</a>`);
        }
    }
};

// Service Worker registration for PWA
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
                console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}

// Initialize components when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize QR Scanner if on a page that needs it
    if (document.querySelector('.qr-scanner')) {
        QRScanner.init();
    }
    
    // Initialize garden map if container exists
    const mapContainer = document.querySelector('#garden-map');
    if (mapContainer) {
        const lat = mapContainer.dataset.lat || 0;
        const lng = mapContainer.dataset.lng || 0;
        GardenMap.init('garden-map', parseFloat(lat), parseFloat(lng));
    }
});
