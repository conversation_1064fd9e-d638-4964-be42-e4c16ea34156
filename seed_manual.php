<?php
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

use App\Models\Garden;
use App\Models\User;
use App\Models\PlantCategory;
use Illuminate\Support\Facades\Hash;

try {
    echo "🌱 Starting manual seeding...\n";
    
    // Create default garden
    $garden = Garden::create([
        'name' => 'Verdantify Botanical Garden',
        'description' => 'A beautiful botanical garden showcasing diverse plant species.',
        'address' => '123 Garden Lane, Green City, GC 12345',
        'latitude' => 40.7128,
        'longitude' => -74.0060,
        'settings' => json_encode([
            'timezone' => 'America/New_York',
            'opening_hours' => [
                'monday' => '9:00-17:00',
                'tuesday' => '9:00-17:00',
                'wednesday' => '9:00-17:00',
                'thursday' => '9:00-17:00',
                'friday' => '9:00-17:00',
                'saturday' => '8:00-18:00',
                'sunday' => '8:00-18:00',
            ],
        ]),
        'is_active' => true,
    ]);
    echo "✅ Garden created: {$garden->name}\n";

    // Create admin user
    $admin = User::create([
        'name' => 'Garden Administrator',
        'email' => '<EMAIL>',
        'password' => Hash::make('password'),
        'role' => 'admin',
        'garden_id' => $garden->id,
        'email_verified_at' => now(),
    ]);
    echo "✅ Admin user created: {$admin->email}\n";

    // Create editor user
    $editor = User::create([
        'name' => 'Garden Editor',
        'email' => '<EMAIL>',
        'password' => Hash::make('password'),
        'role' => 'editor',
        'garden_id' => $garden->id,
        'email_verified_at' => now(),
    ]);
    echo "✅ Editor user created: {$editor->email}\n";

    // Create plant categories
    $categories = [
        ['name' => 'Trees', 'description' => 'Large woody plants with a main trunk', 'color' => '#22c55e', 'icon' => 'tree'],
        ['name' => 'Shrubs', 'description' => 'Woody plants smaller than trees with multiple stems', 'color' => '#16a34a', 'icon' => 'shrub'],
        ['name' => 'Flowers', 'description' => 'Flowering plants and ornamental flowers', 'color' => '#f59e0b', 'icon' => 'flower'],
        ['name' => 'Herbs', 'description' => 'Aromatic plants used for culinary or medicinal purposes', 'color' => '#10b981', 'icon' => 'herb'],
        ['name' => 'Succulents', 'description' => 'Plants with thick, fleshy parts adapted to store water', 'color' => '#06b6d4', 'icon' => 'cactus'],
        ['name' => 'Ferns', 'description' => 'Non-flowering plants that reproduce via spores', 'color' => '#059669', 'icon' => 'fern'],
    ];

    foreach ($categories as $index => $categoryData) {
        $category = PlantCategory::create(array_merge($categoryData, [
            'sort_order' => $index + 1,
            'is_active' => true,
        ]));
        echo "✅ Category created: {$category->name}\n";
    }

    echo "\n🎉 Manual seeding completed successfully!\n";
    echo "📧 Admin login: <EMAIL> / password\n";
    echo "📧 Editor login: <EMAIL> / password\n";
    
} catch(Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
