<?php
require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

try {
    // Test basic database connection
    $pdo = DB::connection()->getPdo();
    echo "✅ Laravel DB connection: SUCCESS\n";
    
    // Test creating a simple table
    DB::statement('CREATE TABLE IF NOT EXISTS test_table (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(255))');
    echo "✅ Table creation: SUCCESS\n";
    
    // Test dropping the table
    DB::statement('DROP TABLE test_table');
    echo "✅ Table deletion: SUCCESS\n";
    
    echo "🎉 Database operations working correctly!\n";
    
} catch(Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
