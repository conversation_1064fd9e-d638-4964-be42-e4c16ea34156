<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Plant extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'scientific_name',
        'description',
        'care_instructions',
        'seasonal_info',
        'garden_id',
        'latitude',
        'longitude',
        'qr_code_path',
        'image_path',
        'is_active',
        'created_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
            'seasonal_info' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the garden this plant belongs to
     */
    public function garden()
    {
        return $this->belongsTo(Garden::class);
    }

    /**
     * Get the user who created this plant
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the categories this plant belongs to
     */
    public function categories()
    {
        return $this->belongsToMany(PlantCategory::class, 'plant_category_plant');
    }

    /**
     * Get all images for this plant
     */
    public function images()
    {
        return $this->hasMany(PlantImage::class);
    }

    /**
     * Get analytics for this plant
     */
    public function analytics()
    {
        return $this->hasMany(PlantAnalytic::class);
    }

    /**
     * Get the QR label for this plant
     */
    public function qrLabel()
    {
        return $this->hasOne(QrLabel::class);
    }

    /**
     * Get the plant's coordinates
     */
    public function getCoordinatesAttribute()
    {
        return [
            'lat' => $this->latitude,
            'lng' => $this->longitude,
        ];
    }

    /**
     * Get the plant's public URL
     */
    public function getPublicUrlAttribute()
    {
        return route('plants.show', $this->id);
    }

    /**
     * Get related plants based on categories
     */
    public function getRelatedPlantsAttribute()
    {
        return Plant::whereHas('categories', function ($query) {
            $query->whereIn('plant_categories.id', $this->categories->pluck('id'));
        })
        ->where('id', '!=', $this->id)
        ->where('is_active', true)
        ->limit(3)
        ->get();
    }

    /**
     * Scope for active plants
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for plants with location
     */
    public function scopeWithLocation($query)
    {
        return $query->whereNotNull('latitude')->whereNotNull('longitude');
    }
}
