# Verdantify

**Smart Plant Labeling System for Botanical Gardens**

Verdantify modernizes plant labeling in botanical gardens and parks through QR codes and a responsive web platform. The system solves the problem of outdated, weathered plant labels while providing rich educational content to visitors.

## Features

- **QR Code Plant Labels**: Weather-resistant QR codes linking to comprehensive plant information
- **Rich Plant Profiles**: Detailed information with images, care instructions, and seasonal data
- **Interactive Garden Maps**: GPS-enabled navigation with plant location tracking
- **Admin Dashboard**: Content management interface for garden staff
- **Progressive Web App**: Mobile-optimized with offline functionality

## Requirements

- PHP 8.2+
- MySQL 8.0 or PostgreSQL 15+
- Composer
- Node.js & NPM (for frontend assets)

### Required PHP Extensions

- OpenSSL
- PDO
- Mbstring
- Tokenizer
- XML
- Ctype
- JSON
- BCMath
- Fileinfo

## Installation

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/verdantify.git
cd verdantify
```

### 2. Install Dependencies

```bash
composer install
npm install
```

### 3. Environment Configuration

```bash
cp .env.example .env
php artisan key:generate
```

Edit `.env` file with your database credentials:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=verdantify
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 4. Database Setup

```bash
php artisan migrate
php artisan db:seed
```

### 5. Storage Setup

```bash
php artisan storage:link
```

### 6. Build Frontend Assets

```bash
npm run build
```

### 7. Start Development Server

```bash
php artisan serve
```

Visit `http://localhost:8000` to access the application.

## Development

### Running Tests

```bash
php artisan test
```

### Code Style

```bash
./vendor/bin/pint
```

### Queue Workers (for background jobs)

```bash
php artisan queue:work
```

## Deployment

### Production Setup

1. Set `APP_ENV=production` in `.env`
2. Set `APP_DEBUG=false` in `.env`
3. Configure your web server (Nginx/Apache)
4. Set up SSL certificates
5. Configure queue workers with Supervisor
6. Set up scheduled tasks with cron

### Server Requirements

- 2+ CPU cores
- 4GB RAM minimum (8GB recommended)
- 50GB+ SSD storage
- Ubuntu 22.04 LTS or similar
- SSL certificate for HTTPS

## API Documentation

API endpoints are available at `/api/documentation` when the application is running.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions, please open an issue on GitHub or contact the development team.

## Roadmap

- [x] Phase 1: Core MVP (Foundation)
- [ ] Phase 2: Enhanced Content Management
- [ ] Phase 3: Visitor Experience Enhancement
- [ ] Phase 4: Analytics and PWA
- [ ] Phase 5: Advanced Features

See the [Product Requirements Document](prd.md) for detailed feature specifications.
