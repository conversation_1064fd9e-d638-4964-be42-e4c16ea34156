<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('qr_labels', function (Blueprint $table) {
            $table->id();
            $table->foreignId('plant_id')->constrained()->onDelete('cascade');
            $table->string('qr_code_path');
            $table->string('label_id')->unique(); // Physical label identifier
            $table->timestamp('print_date')->nullable();
            $table->timestamp('install_date')->nullable();
            $table->enum('condition', ['excellent', 'good', 'fair', 'poor', 'damaged', 'faded', 'missing'])->default('excellent');
            $table->text('notes')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['plant_id', 'is_active']);
            $table->index('condition');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('qr_labels');
    }
};
