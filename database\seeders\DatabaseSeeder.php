<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Garden;
use App\Models\PlantCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create default garden
        $garden = Garden::create([
            'name' => 'Verdantify Botanical Garden',
            'description' => 'A beautiful botanical garden showcasing diverse plant species.',
            'address' => '123 Garden Lane, Green City, GC 12345',
            'latitude' => 40.7128,
            'longitude' => -74.0060,
            'settings' => [
                'timezone' => 'America/New_York',
                'opening_hours' => [
                    'monday' => '9:00-17:00',
                    'tuesday' => '9:00-17:00',
                    'wednesday' => '9:00-17:00',
                    'thursday' => '9:00-17:00',
                    'friday' => '9:00-17:00',
                    'saturday' => '8:00-18:00',
                    'sunday' => '8:00-18:00',
                ],
            ],
            'is_active' => true,
        ]);

        // Create admin user
        $admin = User::create([
            'name' => 'Garden Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'garden_id' => $garden->id,
            'email_verified_at' => now(),
        ]);

        // Create editor user
        User::create([
            'name' => 'Garden Editor',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'editor',
            'garden_id' => $garden->id,
            'email_verified_at' => now(),
        ]);

        // Create plant categories
        $categories = [
            [
                'name' => 'Trees',
                'description' => 'Large woody plants with a main trunk',
                'color' => '#22c55e',
                'icon' => 'tree',
            ],
            [
                'name' => 'Shrubs',
                'description' => 'Woody plants smaller than trees with multiple stems',
                'color' => '#16a34a',
                'icon' => 'shrub',
            ],
            [
                'name' => 'Flowers',
                'description' => 'Flowering plants and ornamental flowers',
                'color' => '#f59e0b',
                'icon' => 'flower',
            ],
            [
                'name' => 'Herbs',
                'description' => 'Aromatic plants used for culinary or medicinal purposes',
                'color' => '#10b981',
                'icon' => 'herb',
            ],
            [
                'name' => 'Succulents',
                'description' => 'Plants with thick, fleshy parts adapted to store water',
                'color' => '#06b6d4',
                'icon' => 'cactus',
            ],
            [
                'name' => 'Ferns',
                'description' => 'Non-flowering plants that reproduce via spores',
                'color' => '#059669',
                'icon' => 'fern',
            ],
        ];

        foreach ($categories as $index => $categoryData) {
            PlantCategory::create(array_merge($categoryData, [
                'sort_order' => $index + 1,
                'is_active' => true,
            ]));
        }

        // Create subcategories for Trees
        $treeCategory = PlantCategory::where('name', 'Trees')->first();
        $treeSubcategories = [
            ['name' => 'Deciduous Trees', 'description' => 'Trees that shed leaves seasonally'],
            ['name' => 'Evergreen Trees', 'description' => 'Trees that retain leaves year-round'],
            ['name' => 'Fruit Trees', 'description' => 'Trees that produce edible fruit'],
        ];

        foreach ($treeSubcategories as $index => $subcategoryData) {
            PlantCategory::create(array_merge($subcategoryData, [
                'parent_id' => $treeCategory->id,
                'color' => $treeCategory->color,
                'icon' => $treeCategory->icon,
                'sort_order' => $index + 1,
                'is_active' => true,
            ]));
        }

        $this->command->info('Database seeded successfully!');
        $this->command->info('Admin login: <EMAIL> / password');
        $this->command->info('Editor login: <EMAIL> / password');
    }
}
