$garden = App\Models\Garden::create(['name' => 'Verdantify Botanical Garden', 'description' => 'A beautiful botanical garden showcasing diverse plant species.', 'address' => '123 Garden Lane, Green City, GC 12345', 'latitude' => 40.7128, 'longitude' => -74.0060, 'settings' => ['timezone' => 'America/New_York'], 'is_active' => true]);

$admin = App\Models\User::create(['name' => 'Garden Administrator', 'email' => '<EMAIL>', 'password' => Hash::make('password'), 'role' => 'admin', 'garden_id' => $garden->id, 'email_verified_at' => now()]);

$editor = App\Models\User::create(['name' => 'Garden Editor', 'email' => '<EMAIL>', 'password' => Hash::make('password'), 'role' => 'editor', 'garden_id' => $garden->id, 'email_verified_at' => now()]);

App\Models\PlantCategory::create(['name' => 'Trees', 'description' => 'Large woody plants with a main trunk', 'color' => '#22c55e', 'icon' => 'tree', 'sort_order' => 1, 'is_active' => true]);

App\Models\PlantCategory::create(['name' => 'Shrubs', 'description' => 'Woody plants smaller than trees with multiple stems', 'color' => '#16a34a', 'icon' => 'shrub', 'sort_order' => 2, 'is_active' => true]);

App\Models\PlantCategory::create(['name' => 'Flowers', 'description' => 'Flowering plants and ornamental flowers', 'color' => '#f59e0b', 'icon' => 'flower', 'sort_order' => 3, 'is_active' => true]);

echo "Seeding completed! Admin: <EMAIL> / password";
