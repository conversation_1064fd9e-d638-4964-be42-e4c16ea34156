@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Verdantify */
@layer base {
    html {
        font-family: 'Figtree', system-ui, sans-serif;
    }
}

@layer components {
    .btn-primary {
        @apply bg-verdant-600 hover:bg-verdant-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
    }
    
    .btn-secondary {
        @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
    }
    
    .card {
        @apply bg-white rounded-lg shadow-md p-6;
    }
    
    .form-input {
        @apply border-gray-300 rounded-md shadow-sm focus:border-verdant-500 focus:ring-verdant-500;
    }
}

/* QR Code specific styles */
.qr-code-container {
    @apply flex justify-center items-center p-4 bg-white rounded-lg shadow-sm;
}

/* Plant card styles */
.plant-card {
    @apply bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200;
}

.plant-card img {
    @apply w-full h-48 object-cover;
}

.plant-card-content {
    @apply p-4;
}

/* Responsive design utilities */
@media (max-width: 640px) {
    .mobile-padding {
        @apply px-4;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn-primary {
        @apply border-2 border-black;
    }
}
