<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QrLabel extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'plant_id',
        'qr_code_path',
        'label_id',
        'print_date',
        'install_date',
        'condition',
        'notes',
        'is_active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'print_date' => 'datetime',
            'install_date' => 'datetime',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the plant this QR label belongs to
     */
    public function plant()
    {
        return $this->belongsTo(Plant::class);
    }

    /**
     * Get the QR code URL
     */
    public function getQrCodeUrlAttribute()
    {
        return $this->qr_code_path ? asset('storage/' . $this->qr_code_path) : null;
    }

    /**
     * Scope for active labels
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for labels that need replacement
     */
    public function scopeNeedsReplacement($query)
    {
        return $query->whereIn('condition', ['damaged', 'faded', 'missing']);
    }
}
