<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Garden extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'address',
        'latitude',
        'longitude',
        'settings',
        'is_active',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'latitude' => 'decimal:8',
            'longitude' => 'decimal:8',
            'settings' => 'array',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get all plants in this garden
     */
    public function plants()
    {
        return $this->hasMany(Plant::class);
    }

    /**
     * Get all users who belong to this garden
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get active plants in this garden
     */
    public function activePlants()
    {
        return $this->plants()->where('is_active', true);
    }

    /**
     * Get the garden's center coordinates
     */
    public function getCenterCoordinatesAttribute()
    {
        return [
            'lat' => $this->latitude,
            'lng' => $this->longitude,
        ];
    }
}
